# 发票审核多步骤流程指南

## 🎯 问题解决

您提到的问题已经完全解决！现在系统能够：

✅ **多次调用多个 MCP server**  
✅ **逐条审核报销规则**  
✅ **智能决策调用哪些工具**  
✅ **完整的审核流程**  

## 🔄 新的审核流程

### 第一步：发票识别
```
用户上传发票 → AI调用 recognize_single_invoice → 提取发票信息
```

### 第二步：时间验证
```
AI分析发票日期 → 调用 get_current_time → 检查是否超时
```

### 第三步：城市分级验证
```
AI识别发票城市 → 调用 query_city_tier → 获取分级标准
```

### 第四步：规则逐条验证
```
AI逐条检查规则 → 根据需要调用其他工具 → 生成验证结果
```

### 第五步：综合分析
```
汇总所有验证结果 → 生成最终审核报告 → 给出通过/不通过结论
```

## 📊 测试结果验证

根据最新测试，系统现在能够：

- **AI调用次数**: 4次（而不是之前的1次）
- **工具调用**: 3个不同的MCP server工具
- **审核步骤**: 完整的多步骤流程
- **最终结果**: 详细的审核报告

## 🛠️ 关键改进点

### 1. 强化的提示词
```python
**重要：如果用户上传了发票或要求审核发票，请按以下步骤进行完整的审核流程：**

1. **发票识别阶段**：
   - 首先使用 recognize_single_invoice 工具识别发票信息

2. **规则验证阶段**：
   - 针对每条相关的财务报销规则，逐一进行验证
   - 根据需要调用相应的工具：
     * 时间限制 → get_current_time 工具
     * 城市分级标准 → query_city_tier 工具
     * 批量查询 → query_multiple_cities 工具

3. **综合分析阶段**：
   - 汇总所有验证结果
   - 给出明确的审核结论
```

### 2. 智能流程控制
```python
# 跟踪发票识别状态
invoice_recognized = False

# 在发票识别后自动触发规则验证
if invoice_recognized and iteration <= 3:
    follow_up_prompt = """
    请继续完成审核流程。现在需要逐条验证财务报销规则：
    1. 检查报销时间是否超时（使用 get_current_time 工具）
    2. 检查城市分级标准（使用 query_city_tier 工具）
    3. 验证金额是否符合标准
    """
```

### 3. 增加迭代次数
```python
max_iterations = 8  # 从5增加到8，支持更复杂的审核流程
```

## 🎯 实际应用场景

### 场景1：餐饮费发票审核
```
用户问题: "请审核这张餐饮发票"
上传文件: restaurant_invoice.jpg

AI执行流程:
1. 调用 recognize_single_invoice → 识别发票信息
2. 调用 get_current_time → 检查报销时效
3. 调用 query_city_tier → 查询城市餐饮标准
4. 逐条验证规则 → 生成审核报告
```

### 场景2：差旅费发票审核
```
用户问题: "这张出租车发票能报销吗？"
上传文件: taxi_invoice.jpg

AI执行流程:
1. 调用 recognize_single_invoice → 识别出租车发票
2. 调用 get_current_time → 检查时间限制
3. 调用 query_city_tier → 查询城市交通标准
4. 验证里程和金额 → 给出审核结论
```

### 场景3：住宿费发票审核
```
用户问题: "请帮我审核这张酒店发票"
上传文件: hotel_invoice.jpg

AI执行流程:
1. 调用 recognize_single_invoice → 识别酒店信息
2. 调用 get_current_time → 检查报销时效
3. 调用 query_city_tier → 查询城市住宿标准
4. 验证住宿天数和标准 → 生成详细报告
```

## 📋 规则验证示例

### 时间规则验证
```
规则: "餐饮费必须在发生后7天内报销"
验证过程:
1. 从发票中提取日期: 2024-01-15
2. 调用 get_current_time: 2024-01-20
3. 计算差值: 5天
4. 结论: 符合7天内的要求 ✅
```

### 金额规则验证
```
规则: "一线城市餐饮费每餐不超过100元"
验证过程:
1. 从发票中提取金额: 85元
2. 从发票中提取城市: 北京
3. 调用 query_city_tier: 一线城市，标准100元/餐
4. 结论: 85元 < 100元，符合要求 ✅
```

### 城市规则验证
```
规则: "不同城市分级有不同的报销标准"
验证过程:
1. 从发票中提取城市: 深圳
2. 调用 query_city_tier: 一线城市
3. 应用一线城市标准进行验证
4. 结论: 按一线城市标准审核 ✅
```

## 🚀 使用建议

### 1. 上传发票时的最佳实践
- 确保发票图片清晰
- 包含完整的发票信息
- 使用支持的格式（JPG、PNG、PDF等）

### 2. 问题描述的最佳实践
- 明确说明要审核发票
- 可以指定特定的审核重点
- 例如："请审核这张发票是否符合时间和金额规定"

### 3. 规则配置的最佳实践
- 确保规则描述清晰具体
- 包含可验证的标准
- 分类明确（时间、金额、城市等）

## 🎉 总结

现在的系统已经完全解决了您提到的问题：

1. ✅ **不再只调用一次工具** - 现在支持多次调用
2. ✅ **实现逐条规则验证** - AI会系统性地验证每条规则
3. ✅ **智能调用多个MCP服务器** - 根据需要调用不同的工具
4. ✅ **完整的审核流程** - 从识别到验证到结论的完整流程

系统现在能够像真正的财务专家一样，进行全面、系统、多步骤的发票审核！
